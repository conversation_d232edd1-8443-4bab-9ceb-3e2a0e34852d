// ==UserScript==
// @name         「絵でわかる日本語」閲覧体験強化
// @namespace    http://tampermonkey.net/
// @version      2025-07-08
// @description  「絵でわかる日本語」サイトの漢字の読み方を括弧書きからルビ表示に自動変換し、広告や不要な要素を非表示にすることで、快適な読書環境を提供します。設定パネルからルビの表示・非表示も簡単に切り替え可能です。
// @icon         https://livedoor.blogimg.jp/edewakaru/imgs/8/c/8cdb7924.png
// <AUTHOR>
// @match        https://www.edewakaru.com/*
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @run-at       document-start
// ==/UserScript==

;(function () {
  ;('use strict')

  // ========================================
  // 立即注入关键样式 - 防止页面闪现和滚动
  // ========================================

  // 立即注入关键的隐藏样式，防止 header 等元素闪现
  ;(function injectCriticalStyles() {
    const criticalCSS = `
      /* 立即隐藏不需要的元素，防止闪现 */
      header#blog-header,
      footer#blog-footer,
      .ldb_menu,
      .article-social-btn,
      .adsbygoogle,
      #ldblog_related_articles_01d4ecf1,
      #ad2 {
        display: none !important;
      }

      /* 防止页面自动滚动 */
      html {
        scroll-behavior: auto !important;
      }

      /* 初始化侧边栏为隐藏状态 */
      aside#sidebar {
        visibility: hidden;
      }
    `

    if (typeof GM_addStyle === 'function') {
      GM_addStyle(criticalCSS)
    } else {
      const style = document.createElement('style')
      style.textContent = criticalCSS
      ;(document.head || document.documentElement).appendChild(style)
    }
  })()

  // ========================================
  // 配置模块 (Config) - 统一管理所有配置数据
  // ========================================

  const Config = {
    // 文本处理规则配置 - 按优先级严格划分的五级处理体系
    processingRules: {
      // 🥇 第一优先级 (最高)：全局豁免清单 - 绝对不可侵犯的内容
      // 实现方式：采用"文本标记与还原"技术，确保后续任何规则都无法触碰
      globalExclusions: new Set(['挙句（に）', '道草（を）', '以上（は）', '人称（私）', '人称（あなた）', '矢先（に）']),

      // 🥈 第二优先级：手动定义的复合词 - 用户配置的词条，拥有极高处理权限
      // 实现方式：在脚本初始化时最先被预处理和加载
      compoundWords: {
        // 2.1 标准复合词（字符串格式，自动分词）
        standardWords: [
          '長い間（ながいあいだ）',
          '座り心地（すわりごこち）',
          '触り心地（さわりごこち）',
          '申し訳（もうしわけ）',
          '出張（しゅっちょう）',
          '大好き（だいすき）',
          '唐揚げ（からあげ）',
          '立ち読み（たちよみ）',
          '１杯（いっぱい）',
          '１回（いっかい）',
          '１泊（いっぱく）',
          '１か月（いっかげつ）',
          '１か月間（いっかげつかん）',
          '試験（しけん）',
          '使用（しよう）',
          '前（まえ）',
          '待（ま）',
          '日記（にっき）',
          '話し手（はなして）',
          '聞き手（ききて）',
          '以上（いじょう）',
          '使い方（つかいかた）',
          '０点（れいてん）',
          '買い物（かいもの）',
          '動作（どうさ）',
          'm（メートル）',
          '味覚 （みかく）',
          '気持ち（きもち）',
          '青い色（あおいいろ）',
          '吐き気（はきけ）',
          '元カレ（もとかれ）',
          '髪の毛（かみのけ）',
          '駅（えき）',
          '万引き（まんびき）',
          '通（どお）',
          '遅刻（ちこく）',
          '経（た）',
          '三分の一（さんぶんのいち）',
          '折があれば（おりがあれば）',
          '折を見て（おりをみて）',
          '折に触れて（おりにふれて）',
          '折も折（おりもおり）',
          '残業（ざんぎょう）',
          '合（あ）',
          '楽（たの）',
          '貸し借り（かしかり）',
          '入学（にゅうがく）',
          '暮（ぐ）',
          '届け出（とどけで）',
          '有名（ゆうめい）',
          '自身（じしん）',
          '住（す）',
          '夕ご飯（ゆうごはん）',
          '星の数（ほしのかず）',
          '窓の外（まどのそと）',
          '考え方（かんがえかた）',
          '感じ方（かんじかた）',
          '貯（た）',
          '悩み事（なやみごと）',
          '歩（ある）',
          '食べず嫌い（たべずぎらい）',
          'アタック（attack）',
          'お茶する（おちゃする）',
          '入（はい）',
          '使い分け（つかいわけ）',
          '行き渡る（いきわたる）',
          '星の数ほどある（ほしのかずほどある）',
          '星の数ほどいる（ほしのかずほどいる）',
          '５日間（いつかかん）',
          '食べ物（たべもの）',
          'お団子（おだんご）',
          '足が早い（あしがはやい）',
          'ゴールデンウイーク（Golden Week）',
          '昭和の日（しょうわのひ）',
        ],

        // 2.2 强制注音（指定读音，自动分词）
        forcedReadingWords: [
          { pattern: '羽根を伸ばす（羽根を伸ばす）', reading: 'はねをのばす' },
          { pattern: '長蛇の列（長蛇の列）', reading: 'ちょうだのれつ' },
          { pattern: '付き合（つきあい）', reading: 'つきあ' },
        ],

        // 2.3 强制替换（完全自定义HTML替换内容）
        forcedReplacementWords: [
          { pattern: '目に余る②（めにあまる）', replacement: '<ruby>目<rt>め</rt></ruby>に<ruby>余<rt>あま</rt></ruby>る②' },
          { pattern: '言い方（いいかた）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>' },
          { pattern: '言い訳（いいわけ）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>訳<rt>わけ</rt></ruby>' },
          { pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）', replacement: '<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える' },
          { pattern: '水の泡になる・水の泡となる（みずのあわになる）', replacement: '<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>になる・<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>となる' },
          { pattern: '意味で（いみ）', replacement: '<ruby>意味<rt>いみ</rt></ruby>で' },
          { pattern: '和製英語で（わせいえいご）', replacement: '<ruby>和製英語<rt>わせいえいご</rt></ruby>で' },
        ],
      },

      // 🏅 第四优先级：通用格式转换 - 作为"兜底"方案，处理所有剩下的、符合标准格式的词语
      // 实现方式：在所有特例都处理完毕后，执行 katakana(romaji) 和 kanji(reading) 的正则转换

      // 🎖️ 第五优先级 (最低)：条件豁免规则 - 作为通用格式转换的一个"补丁"，处理一些可能误伤的情况
      // 实现方式：将此规则作为第四优先级内部的一个过滤器来使用
      conditionalExclusions: new Set(['に', 'は', 'を', 'が', 'の', 'と', 'で', 'から', 'まで', 'へ', 'も', 'や', 'ね', 'よ', 'さ']),
    },

    // HTML 替换规则配置 - 管理需要在 HTML 级别进行的替换规则
    htmlReplacement: {
      rules: [
        { pattern: /一瞬（いっしゅん<br>）/g, replacement: '<ruby>一瞬<rt>いっしゅん</rt></ruby>' },
        { pattern: /<b><span style="font-size: 125%;">居<\/span><\/b>（い）/g, replacement: '<b><ruby>居<rt>い</rt></ruby></b>' },
        { pattern: /<b style="font-size: large;">留守<\/b>（るす）/g, replacement: '<b><ruby>留守<rt>るす</rt></ruby></b>' },
      ],
    },

    regex: {
      // 预编译的正则表达式模式
      patterns: {
        // 第三优先级：通用注音格式匹配规则
        ruby: /([一-龯々]+)（([^（）]*)）/g, // 汉字（平假名）格式匹配
        katakana: /([\u30A0-\u30FF]+)[（(]([\w\s+]+)[）)]/g, // 片假名（英文）格式匹配
        bracket: /[【「](?:.*?)([^【】「」（）・、\s]+)（([^（）]*)）([^【】「」（）]*)[】」]/g, // 括号内注音格式匹配

        // 字符类型检测 - 预编译测试函数，用于豁免清单判断
        kanaOnly: /^[\u3040-\u309F]+$/, // 检测是否全为平假名
        nonKana: /[^\u3040-\u309F]/, // 检测是否包含非平假名字符
        isKanaChar: /^[\u3040-\u309F]$/, // 检测单个字符是否为平假名

        // 图片链接处理
        imgSrc: /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i,
      },

      // 正则表达式测试函数缓存 - 避免重复编译和提高性能
      testers: {
        isKanaOnly: (text) => Config.regex.patterns.kanaOnly.test(text),
        hasNonKana: (text) => Config.regex.patterns.nonKana.test(text),
        isKanaChar: (char) => Config.regex.patterns.isKanaChar.test(char),
      },
    },

    // 系统常量配置 - 管理 DOM 节点类型、选择器等系统级常量
    constants: {
      // DOM 节点类型常量
      nodeTypes: {
        TEXT: Node.TEXT_NODE,
        ELEMENT: Node.ELEMENT_NODE,
      },

      // 空白节点配置
      whitespaceNode: {
        // 空白文本正则
        regex: /^\s*$/,
        // 需要移除的元素标签
        emptyTags: new Set(['BR']),
        // 需要移除的特殊内容元素
        specialContent: new Map([['SPAN', '&nbsp;']]),
      },

      // 全局移除的选择器
      globalRemoveSelectors: ['header#blog-header', 'footer#blog-footer', '.ldb_menu', '.article-social-btn', '.adsbygoogle', 'a[href*="blogmura.com"]', 'a[href*="with2.net"]'],
    },

    // 设置项管理
    settings: {
      keys: {
        SCRIPT_ENABLED: 'ruby_converter_enabled',
        FURIGANA_VISIBLE: 'furigana_visible',
      },

      // 设置项配置数组 - 在 UI 模块定义后进行配置
      config: [],
    },
  }

  // ========================================
  // 工具模块 (Utils) - 提供通用的工具函数
  // ========================================

  const Utils = {
    // 正则表达式工具 - 提供正则相关的工具函数
    regex: {
      /**
       * 转义正则表达式特殊字符
       * @param {string} string - 需要转义的字符串
       * @returns {string} 转义后的字符串
       */
      escape: (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
    },

    // 存储工具 - 提供统一的存储操作接口
    storage: {
      /**
       * 从存储中获取设置值
       * @param {string} key - 设置键名
       * @param {*} defaultValue - 默认值
       * @returns {*} 设置值
       */
      get: (key, defaultValue) => GM_getValue(key, defaultValue),

      /**
       * 将设置值保存到存储中
       * @param {string} key - 设置键名
       * @param {*} value - 设置值
       */
      set: (key, value) => GM_setValue(key, value),
    },

    // DOM 工具 - 提供 DOM 操作相关的工具函数
    dom: {
      /**
       * 判断一个节点是否为"空白节点"（如空文本、<br>、&nbsp;等）
       * @param {Node} node - 需要检查的节点
       * @returns {boolean} 是否为空白节点
       */
      isWhitespaceNode: (node) => {
        if (!node) return false

        const config = Config.constants.whitespaceNode

        // 检查是否为空白文本节点
        if (node.nodeType === Config.constants.nodeTypes.TEXT && config.regex.test(node.textContent)) {
          return true
        }

        // 检查是否为需要移除的元素节点
        if (node.nodeType === Config.constants.nodeTypes.ELEMENT) {
          const tagName = node.tagName

          // 检查是否为空标签（如 <br>）
          if (config.emptyTags.has(tagName)) {
            return true
          }

          // 检查是否为特殊内容元素（如 <span>&nbsp;</span>）
          const expectedContent = config.specialContent.get(tagName)
          if (expectedContent && node.innerHTML === expectedContent) {
            return true
          }
        }

        return false
      },

      /**
       * 移除容器开头和结尾的空白文本节点、换行符和空格元素
       * @param {HTMLElement} container - 需要清理的容器元素
       */
      trimContainerBreaks: (container) => {
        if (!container || !container.nodeType) return

        // 从开头清理空白节点
        while (Utils.dom.isWhitespaceNode(container.firstChild)) {
          container.removeChild(container.firstChild)
        }

        // 从结尾清理空白节点
        while (Utils.dom.isWhitespaceNode(container.lastChild)) {
          container.removeChild(container.lastChild)
        }
      },
    },
  }

  // ========================================
  // 核心处理模块 (Core) - 核心业务逻辑处理
  // ========================================

  /**
   * 核心处理模块 - 集中管理核心业务逻辑，包括数据预处理、文本处理、DOM 管理
   *
   * 五级优先级处理体系：
   * 🥇 第一优先级 (最高)：全局豁免 (Config.processingRules.globalExclusions) - 绝对不可侵犯的内容
   * 🥈 第二优先级：手动定义的复合词 (Config.processingRules.compoundWords) - 用户配置的词条，拥有极高处理权限
   * 🥉 第三优先级：自动学习的复合词 (findAndRegisterCompounds) - 动态从页面中学习新词，但不能覆盖用户的手动配置
   * 🏅 第四优先级：通用格式转换 (Config.regex.patterns.ruby/katakana) - 作为"兜底"方案，处理所有剩下的、符合标准格式的词语
   * 🎖️ 第五优先级 (最低)：条件豁免规则 (Config.processingRules.conditionalExclusions) - 作为通用格式转换的一个"补丁"
   */
  const Core = {
    /**
     * 数据预处理器 - 负责预处理复合词数据，优化运行时性能
     * 此模块为第二优先级处理提供数据支持
     */
    dataProcessor: {
      // 处理后的复合词数据结构
      processedWords: {
        // 需要分割处理的词汇：Map <首字符，Array<{pattern, kanji, reading, regex}>>
        segmentWords: new Map(),
        // 直接替换的词汇：Map <首字符，Array<{pattern, replacement, regex}>>
        replaceWords: new Map(),
        // 全局正则表达式，用于一次性匹配所有可能的模式
        globalRegex: null,
        // 模式到预计算结果的映射，避免函数调用开销
        patternResults: new Map(),
      },

      // 动态词汇管理
      dynamicWords: new Set(),

      /**
       * 预处理复合词数据，将三种不同格式的复合词数据转换为优化的数据结构
       * 第二优先级：为复合词特例精确匹配构建优化的数据结构
       * @returns {Object} 处理后的数据结构
       */
      preprocess: () => {
        const segmentWords = new Map()
        const replaceWords = new Map()
        const allPatterns = []
        const patternResults = new Map()

        // 步骤1：处理标准复合词（字符串格式，自动分词）
        Config.processingRules.compoundWords.standardWords.forEach((entryString) => {
          const parsed = Core.dataProcessor.parseStringEntry(entryString)
          if (!parsed) return

          const firstChar = parsed.pattern[0]
          const escapedPattern = Utils.regex.escape(parsed.pattern)

          // 分割处理类型
          if (!segmentWords.has(firstChar)) {
            segmentWords.set(firstChar, [])
          }
          const wordData = {
            pattern: parsed.pattern,
            kanji: parsed.kanji,
            reading: parsed.reading,
            regex: new RegExp(escapedPattern, 'g'),
          }
          segmentWords.get(firstChar).push(wordData)
          // 预计算分割结果，避免运行时计算
          const precomputedResult = Core.textProcessor.segmentWord(parsed.kanji, parsed.reading)
          patternResults.set(parsed.pattern, precomputedResult)
          allPatterns.push(escapedPattern)
        })

        // 步骤2：处理强制注音词（指定读音，自动分词）
        Config.processingRules.compoundWords.forcedReadingWords.forEach((entryObject) => {
          const parsed = Core.dataProcessor.parseObjectEntry(entryObject)
          if (!parsed) return

          const firstChar = parsed.pattern[0]
          const escapedPattern = Utils.regex.escape(parsed.pattern)

          // 分割处理类型
          if (!segmentWords.has(firstChar)) {
            segmentWords.set(firstChar, [])
          }
          const wordData = {
            pattern: parsed.pattern,
            kanji: parsed.kanji,
            reading: parsed.reading,
            regex: new RegExp(escapedPattern, 'g'),
          }
          segmentWords.get(firstChar).push(wordData)
          // 预计算分割结果，避免运行时计算
          const precomputedResult = Core.textProcessor.segmentWord(parsed.kanji, parsed.reading)
          patternResults.set(parsed.pattern, precomputedResult)
          allPatterns.push(escapedPattern)
        })

        // 步骤3：处理强制替换词（完全自定义HTML替换内容）
        Config.processingRules.compoundWords.forcedReplacementWords.forEach((entryObject) => {
          const pattern = entryObject.pattern
          const replacement = entryObject.replacement
          const firstChar = pattern[0]
          const escapedPattern = Utils.regex.escape(pattern)

          // 直接替换类型
          if (!replaceWords.has(firstChar)) {
            replaceWords.set(firstChar, [])
          }
          const wordData = {
            pattern: pattern,
            replacement: replacement,
            regex: new RegExp(escapedPattern, 'g'),
          }
          replaceWords.get(firstChar).push(wordData)
          // 直接存储结果，避免函数调用
          patternResults.set(pattern, replacement)
          allPatterns.push(escapedPattern)
        })

        // 创建全局正则表达式，用于一次性匹配所有模式
        const globalRegex = allPatterns.length > 0 ? new RegExp(`(${allPatterns.join('|')})`, 'g') : null

        return {
          segmentWords,
          replaceWords,
          globalRegex,
          patternResults,
        }
      },

      /**
       * 解析不同格式的复合词条目，支持字符串格式和对象格式
       * 为第二优先级处理提供数据解析支持
       * @param {string|object} entry - 复合词条目，可以是字符串或对象
       * @returns {object|null} 解析后的对象，包含 pattern、kanji、reading 或 replacement 属性
       */
      parseEntry: (entry) => {
        // 字符串格式："汉字（注音）"
        if (typeof entry === 'string') {
          const leftIdx = entry.indexOf('（')
          const rightIdx = entry.lastIndexOf('）')
          if (leftIdx > 0 && rightIdx > leftIdx) {
            return {
              pattern: entry,
              kanji: entry.slice(0, leftIdx),
              reading: entry.slice(leftIdx + 1, rightIdx),
            }
          }
        }
        // 对象格式：{ pattern: '...', reading: '...' }
        else if (entry && entry.reading) {
          return {
            pattern: entry.pattern,
            kanji: entry.pattern.replace(/（.*?）/, ''),
            reading: entry.reading,
          }
        }
        // 对象格式：{ pattern: '...', replacement: '...' }
        else if (entry && entry.replacement) {
          return entry
        }
        return null
      },

      /**
       * 解析字符串格式的复合词条目："汉字（注音）"
       * @param {string} entryString - 字符串格式的复合词条目
       * @returns {object|null} 解析后的对象，包含 pattern、kanji、reading 属性
       */
      parseStringEntry: (entryString) => {
        const leftIdx = entryString.indexOf('（')
        const rightIdx = entryString.lastIndexOf('）')
        if (leftIdx > 0 && rightIdx > leftIdx) {
          return {
            pattern: entryString,
            kanji: entryString.slice(0, leftIdx),
            reading: entryString.slice(leftIdx + 1, rightIdx),
          }
        }
        return null
      },

      /**
       * 解析对象格式的复合词条目：{ pattern: '...', reading: '...' }
       * @param {object} entryObject - 对象格式的复合词条目
       * @returns {object|null} 解析后的对象，包含 pattern、kanji、reading 属性
       */
      parseObjectEntry: (entryObject) => {
        if (entryObject && entryObject.pattern && entryObject.reading) {
          return {
            pattern: entryObject.pattern,
            kanji: entryObject.pattern.replace(/（.*?）/, ''),
            reading: entryObject.reading,
          }
        }
        return null
      },

      /**
       * 重新构建包含所有模式的全局正则表达式
       */
      rebuildGlobalRegex: () => {
        const allPatterns = []
        const processedWords = Core.dataProcessor.processedWords

        // 收集所有分割处理词汇的模式
        for (const words of processedWords.segmentWords.values()) {
          for (const word of words) {
            allPatterns.push(Utils.regex.escape(word.pattern))
          }
        }
        // 收集所有直接替换词汇的模式
        for (const words of processedWords.replaceWords.values()) {
          for (const word of words) {
            allPatterns.push(Utils.regex.escape(word.pattern))
          }
        }
        // 重建全局正则表达式
        processedWords.globalRegex = allPatterns.length > 0 ? new RegExp(`(${allPatterns.join('|')})`, 'g') : null
      },
    },

    /**
     * 文本处理器 - 负责文本内容的转换和处理
     */
    textProcessor: {
      /**
       * 第二优先级：为预处理的复合词生成 Ruby 标签
       * @param {string} kanji - 汉字部分
       * @param {string} reading - 读音部分
       * @returns {string} 生成的 HTML Ruby 标签字符串
       */
      segmentWord: (kanji, reading) => {
        // 使用直接字符串拼接，避免数组创建和 join 操作
        let result = ''
        let kanjiIndex = 0
        let readingIndex = 0

        while (kanjiIndex < kanji.length) {
          // 检查当前字符是否为平假名 - 使用优化的正则表达式
          if (Config.regex.testers.isKanaChar(kanji[kanjiIndex])) {
            // 如果是平假名，直接添加到结果中
            result += kanji[kanjiIndex]
            kanjiIndex++
            // 在注音中找到对应的平假名位置
            readingIndex = reading.indexOf(kanji[kanjiIndex - 1], readingIndex) + 1
          } else {
            // 处理连续的汉字部分
            let kanjiPart = ''
            let readingPart = ''
            // 收集连续的汉字
            while (kanjiIndex < kanji.length && !Config.regex.testers.isKanaChar(kanji[kanjiIndex])) {
              kanjiPart += kanji[kanjiIndex]
              kanjiIndex++
            }
            // 确定对应的注音部分
            const nextKanaIndex = kanjiIndex < kanji.length ? reading.indexOf(kanji[kanjiIndex], readingIndex) : reading.length
            readingPart = reading.substring(readingIndex, nextKanaIndex)
            readingIndex = nextKanaIndex
            // 直接拼接 ruby 标签，避免创建临时数组元素
            result += `<ruby>${kanjiPart}<rt>${readingPart}</rt></ruby>`
          }
        }
        return result
      },

      /**
       * 将文本中的注音格式转换为 Ruby 标签，使用预计算结果避免函数调用开销
       * 核心处理函数 - 按优先级顺序执行所有转换规则
       * @param {string} text - 需要处理的文本内容
       * @returns {string} 处理后的文本内容，包含 Ruby 标签
       */
      processContent: (text) => {
        // 提前检查文本是否包含可能的注音格式，避免不必要的处理
        if (!text.includes('（') && !text.includes('(')) {
          return text
        }

        const processedWords = Core.dataProcessor.processedWords

        // 第二优先级：复合词特例精确匹配 - 使用全局正则表达式一次性匹配所有复合词模式
        if (processedWords.globalRegex) {
          text = text.replace(processedWords.globalRegex, (match) => {
            // 直接从 Map 获取预计算结果，避免函数调用开销
            const result = processedWords.patternResults.get(match)
            return result || match
          })
        }

        // 🏅 第四优先级：通用格式转换 - 作为"兜底"方案，处理所有剩下的、符合标准格式的词语
        // 片假名 + 英文注音处理（支持全角/半角括号）
        text = text.replace(Config.regex.patterns.katakana, (_, katakana, romaji) => {
          // 直接整体作为注音，不拆分 +
          return `<ruby>${katakana}<rt>${romaji}</rt></ruby>`
        })

        // 🏅 第四优先级：汉字（平假名）注音处理 - 通用的正则表达式模式匹配转换
        return text.replace(Config.regex.patterns.ruby, (_, kanji, reading) => {
          const fullMatch = kanji + '（' + reading + '）'
          // 🥇 第一优先级：全局豁免检查 - 绝对不可侵犯
          if (Config.processingRules.globalExclusions.has(fullMatch)) return _
          // 🎖️ 第五优先级：条件豁免规则 - 排除助词且 kanji 全为平假名
          if (Config.processingRules.conditionalExclusions.has(reading) && Config.regex.testers.isKanaOnly(kanji)) return _
          // 排除非假名注音
          if (Config.regex.testers.hasNonKana(reading)) return _
          return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : _
        })
      },
    },

    /**
     * DOM 管理器 - 负责 DOM 操作和内容清理
     */
    domManager: {
      /**
       * 将博客图片链接转换为直接的图片元素，并优化图片显示
       * @param {HTMLElement} container - 包含图片链接的容器元素
       */
      processImageLinks: (container) => {
        const imageLinks = container.querySelectorAll('a[href*="livedoor.blogimg.jp"]')
        imageLinks.forEach((link) => {
          const img = link.querySelector('img.pict')
          if (!img) return
          // 获取原始图片 URL（移除 -s 后缀）
          const originalSrc = img.src.replace(Config.regex.patterns.imgSrc, '$1$2')
          // 创建新的图片元素
          const newImg = document.createElement('img')
          newImg.src = originalSrc
          newImg.alt = (img.alt || '').replace(/blog/gi, '')
          newImg.className = img.className
          newImg.width = img.width
          newImg.height = img.height
          // 替换链接为图片
          link.replaceWith(newImg)
        })
      },

      /**
       * 批量移除页面中不需要的全局元素，如广告、菜单等
       */
      removeGlobalElements: () => {
        // 合并选择器，一次查询完成所有元素移除，减少 DOM 遍历次数
        const combinedSelector = Config.constants.globalRemoveSelectors.join(',')
        const elementsToRemove = document.querySelectorAll(combinedSelector)
        // 批量移除元素，减少重排重绘
        elementsToRemove.forEach((el) => el.remove())
      },

      /**
       * 清理文章容器中的不需要元素，包括广告、脚本、不需要的链接等
       * @param {HTMLElement} container - 文章容器元素
       */
      cleanupContent: (container) => {
        // 处理图片链接
        Core.domManager.processImageLinks(container)
        // 批量收集需要移除的元素，减少 DOM 查询次数
        const elementsToRemove = []
        // 收集不需要的链接
        const unwantedLinks = container.querySelectorAll('a[href*="blogmura.com"], a[href*="with2.net"]')
        elementsToRemove.push(...unwantedLinks)
        // 收集脚本元素
        const scripts = container.querySelectorAll('script')
        elementsToRemove.push(...scripts)
        // 查找 ad2 元素并收集其后续元素
        const adDiv = container.querySelector('#ad2')
        if (adDiv) {
          // 收集 ad2 之后的所有元素
          let nextElement = adDiv.nextElementSibling
          while (nextElement) {
            elementsToRemove.push(nextElement)
            nextElement = nextElement.nextElementSibling
          }
          // 收集 ad2 本身
          elementsToRemove.push(adDiv)
        }
        // 批量移除所有收集的元素，减少重排重绘次数
        elementsToRemove.forEach((el) => el.remove())
        // 清理容器末尾的空白和换行
        Utils.dom.trimContainerBreaks(container)
      },

      /**
       * 清理侧边栏内容，只保留分类插件，并设置为可见
       */
      optimizeSidebar: () => {
        const sidebar = document.querySelector('aside#sidebar')
        if (!sidebar) return
        const category = sidebar.querySelector('.plugin-categorize')
        sidebar.textContent = ''
        if (category) {
          sidebar.appendChild(category)
          // 显示处理完成的侧边栏
          sidebar.style.visibility = 'visible'
        }
      },
    },
  }

  // ========================================
  // 样式模块 (Styles) - 管理所有 CSS 样式
  // ========================================

  const Styles = {
    constants: {
      // 统一设置面板和开关按钮的样式
      toggleStyles: `
        #settings-panel { position: fixed; bottom: 1.3rem; right: 1rem; z-index: 9999; display: flex; flex-direction: column; gap: 0.5rem; padding: 1rem; background: white; border-radius: 4px; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); width: 140px; opacity: 0.8; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }
        .settings-title { font-size: 0.875rem; font-weight: 600; color: #1F2937; margin: 0 0 0.375rem 0; text-align: center; border-bottom: 1px solid #E5E7EB; padding-bottom: 0.375rem; }
        .setting-item { display: flex; align-items: center; justify-content: space-between; gap: 0.5rem; }
        .setting-label { font-size: 0.8125rem; font-weight: 500; color: #4B5563; cursor: pointer; flex: 1; line-height: 1.2; }
        .toggle-switch { position: relative; display: inline-block; width: 2.5rem; height: 1.25rem; flex-shrink: 0; }
        .toggle-switch input { opacity: 0; width: 0; height: 0; }
        .toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #E5E7EB; transition: all 0.2s ease-in-out; border-radius: 9999px; }
        .toggle-slider:before { position: absolute; content: ""; height: 0.9375rem; width: 0.9375rem; left: 0.15625rem; bottom: 0.15625rem; background-color: white; transition: all 0.2s ease-in-out; border-radius: 50%; box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1),0 1px 2px 0 rgba(0,0,0,0.06); }
        input:checked+.toggle-slider { background-color: #3B82F6; }
        input:checked+.toggle-slider:before { transform: translateX(1.25rem); }
        .toggle-slider:hover { background-color: #D1D5DB; }
        input:checked+.toggle-slider:hover { background-color: #2563EB; }
        .settings-notification { position: fixed; bottom: 9rem; right: 1rem; z-index: 10000; padding: 0.5rem 0.75rem; background-color: #3B82F6; color: white; border-radius: 0.375rem; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); font-size: 0.8125rem; font-weight: 500; animation: slideInOut 3s ease-in-out; white-space: nowrap; }
        @keyframes slideInOut { 0% { opacity: 0; transform: translateX(20px); } 15% { opacity: 1; transform: translateX(0); } 85% { opacity: 1; transform: translateX(0); } 100% { opacity: 0; transform: translateX(20px); } }
      `,

      // 初始页面布局和样式优化
      pageStyles: `
        /* 基础布局 */
        #container { width: 100%; }
        @media (min-width: 960px) { #container { max-width: 960px; } }
        @media (min-width: 1040px) { #container { max-width: 1040px; } }
        #content { display: flex; position: relative; padding: 50px 0 !important; }
        #main { flex: 1; float: none !important; width: 100% !important; }

        /* 侧边栏样式 */
        aside#sidebar { float: none !important; width: 350px !important; flex: 0 0 350px; }
        .plugin-categorize { position: fixed; height: 85vh; display: flex; flex-direction: column; padding: 0 !important; width: 350px !important; }
        .plugin-categorize .side { flex: 1; overflow-y: auto; max-height: unset; }
        .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) { margin-top: 5px; margin-bottom: 0; }

        /* 文章样式 */
        .article { padding: 0 0 20px 0 !important; margin-bottom: 30px !important; }
        .article-body { padding: 0 !important; }
        .article-pager { margin-bottom: 0 !important; }
        .article-body-inner { line-height: 2; }
        .article-body-inner img.pict { margin: 0 !important; width: 80% !important; display: block; }
        .article-body-inner strike { color: orange; }
        .article-body-inner iframe { margin: 4px 0 !important; }

        /* 其他元素 */
        .to-pagetop { position: fixed; bottom: 1.2rem; right: 220px; z-index: 1000; }
        rt, iframe, .pager { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }

        /* 清除浮动样式覆盖 */
        .article-body-inner:after, .article-meta:after, #container:after, #content:after, article:after, section:after, .cf:after { content: none !important; display: none !important; height: auto !important; visibility: visible !important; }

        /* 防止页面跳转和滚动 */
        html, body { scroll-behavior: auto !important; }
        body { overflow-anchor: none !important; }
      `,
    },

    manager: {
      /**
       * 通用 CSS 注入函数
       * 优先使用 GM_addStyle，如果不可用则创建 <style> 标签注入到页面
       * @param {string} cssText - 要注入的 CSS 文本字符串
       */
      inject: (cssText) => {
        if (typeof GM_addStyle === 'function') {
          GM_addStyle(cssText)
        } else {
          const style = document.createElement('style')
          style.textContent = cssText
          ;(document.head || document.documentElement).appendChild(style)
        }
      },

      /**
       * 切换振假名显示状态，通过修改 CSS 样式来控制所有 rt 元素的显示/隐藏
       * @param {boolean} visible - 是否显示振假名
       */
      toggleFurigana: (visible) => {
        const styleId = 'furigana-display-style'
        let styleElement = document.getElementById(styleId)
        if (!styleElement) {
          styleElement = document.createElement('style')
          styleElement.id = styleId
          document.head.appendChild(styleElement)
        }
        const displayValue = visible ? 'ruby-text' : 'none'
        styleElement.textContent = `rt { display: ${displayValue} !important; }`
      },
    },
  }

  // ========================================
  // 用户界面模块 (UI) - 管理用户界面组件
  // ========================================

  const UI = {
    components: {
      // 创建开关组件
      createToggleSwitch: (config) => {
        const currentValue = Utils.storage.get(config.key, config.defaultValue)
        const settingId = `setting-${config.key.replace(/_/g, '-')}`
        const container = document.createElement('div')
        container.className = 'setting-item'
        container.innerHTML = `
          <label for="${settingId}" class="setting-label" title="${config.description}">
            ${config.label}
          </label>
          <label class="toggle-switch">
            <input type="checkbox" id="${settingId}" ${currentValue ? 'checked' : ''}>
            <span class="toggle-slider"></span>
          </label>
        `
        // 添加事件监听器
        const checkbox = container.querySelector('input')
        checkbox.addEventListener('change', (e) => {
          config.handler(e.target.checked)
        })
        return container
      },

      // 创建通知组件
      createNotification: (message) => {
        const notification = document.createElement('div')
        notification.className = 'settings-notification'
        notification.textContent = message
        document.body.appendChild(notification)
        setTimeout(() => notification.remove(), 2000)
      },
    },

    settingsPanel: {
      element: null,
      // 创建包含所有开关的统一设置面板
      create: () => {
        const panel = document.createElement('div')
        panel.id = 'settings-panel'
        // 创建面板标题
        const title = document.createElement('h3')
        title.className = 'settings-title'
        title.textContent = '設定パネル'
        panel.appendChild(title)
        // 为每个设置创建开关
        Config.settings.config.forEach((config) => {
          const settingItem = UI.components.createToggleSwitch(config)
          panel.appendChild(settingItem)
        })
        document.body.appendChild(panel)
        UI.settingsPanel.element = panel
      },
    },

    eventHandlers: {
      /**
       * 处理主脚本功能的开启
       * @param {boolean} enabled - 是否启用脚本
       */
      handleScriptToggle: (enabled) => {
        Utils.storage.set(Config.settings.keys.SCRIPT_ENABLED, enabled)
        UI.components.createNotification('設定を保存しました。ページを再読み込みしてください。')
      },

      /**
       * 处理振假名显示切换
       * @param {boolean} visible - 是否显示振假名
       */
      handleFuriganaToggle: (visible) => {
        Utils.storage.set(Config.settings.keys.FURIGANA_VISIBLE, visible)
        Styles.manager.toggleFurigana(visible)
      },
    },
  }

  // 配置设置项 - 在 UI 模块定义后进行配置
  Config.settings.config = [
    {
      key: Config.settings.keys.SCRIPT_ENABLED,
      label: 'ページ最適化',
      defaultValue: true,
      description: 'ページの最適化とコンテンツクリーニング機能を有効にします',
      handler: UI.eventHandlers.handleScriptToggle,
    },
    {
      key: Config.settings.keys.FURIGANA_VISIBLE,
      label: '振り仮名表示',
      defaultValue: true,
      description: '振り仮名の表示・非表示を切り替えます',
      handler: UI.eventHandlers.handleFuriganaToggle,
    },
  ]

  // ========================================
  // 应用程序模块 (App) - 主控制器，按优先级顺序调用各个处理模块
  // ========================================

  const App = {
    // 应用状态 - 管理应用程序的运行状态
    state: {
      initialized: false,
      scriptEnabled: true,
      furiganaVisible: true,
    },

    // 初始化应用程序 - 处理流程的入口函数
    init: () => {
      if (App.state.initialized) return
      // 检查脚本是否启用
      App.state.scriptEnabled = Utils.storage.get(Config.settings.keys.SCRIPT_ENABLED, true)
      if (!App.state.scriptEnabled) {
        App.initMinimalMode()
        return
      }
      App.initFullMode()
      App.state.initialized = true
    },

    // 最小模式初始化（仅显示设置面板）
    initMinimalMode: () => {
      Styles.manager.inject(Styles.constants.toggleStyles)
      UI.settingsPanel.create()
    },

    // 完整模式初始化
    initFullMode: () => {
      // 保存当前滚动位置，防止页面跳转
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop

      // 注入样式
      Styles.manager.inject(Styles.constants.toggleStyles)
      Styles.manager.inject(Styles.constants.pageStyles)
      // 预处理数据
      Core.dataProcessor.processedWords = Core.dataProcessor.preprocess()
      // 启动主要功能
      App.runMainProcess()
      // 创建 UI
      UI.settingsPanel.create()
      // 初始化振假名显示
      App.initFuriganaDisplay()

      // 恢复滚动位置
      requestAnimationFrame(() => {
        window.scrollTo(0, scrollTop)
      })
    },

    // 运行主要处理流程
    runMainProcess: () => {
      // 立即移除全局元素
      Core.domManager.removeGlobalElements()
      // 处理主要内容
      App.processMainContent()
    },

    // 处理主要内容
    processMainContent: () => {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      if (articleBodies.length === 0) {
        // 重试机制，最多重试 3 次，每次间隔 100ms
        App.retryProcessContent(0)
        return
      }
      // 分批处理，避免阻塞
      App.processBatch(articleBodies, 0)
    },

    /**
     * 分批处理文章内容 - 按优先级顺序执行所有处理步骤
     * @param {NodeList} elements - 要处理的元素列表
     * @param {number} currentIndex - 当前处理的索引
     */
    processBatch: (elements, currentIndex) => {
      const batchSize = Math.min(2, elements.length - currentIndex)
      const endIndex = currentIndex + batchSize

      for (let i = currentIndex; i < endIndex; i++) {
        const body = elements[i]
        // 预处理：清理DOM结构和不需要的元素
        Core.domManager.cleanupContent(body)
        // 第一优先级：HTML级别强行替换 - 最高优先级处理
        App.applyHtmlReplacements(body, Config.htmlReplacement.rules)
        // 动态学习：发现并注册新的复合词到第二优先级系统
        App.findAndRegisterCompounds(body)
        // 执行所有优先级的文本转换：第二优先级 → 第三优先级 → 豁免清单检查
        App.processRubyConversion(body)
        // 显示处理完成的内容
        body.style.opacity = 1
      }

      if (endIndex < elements.length) {
        requestAnimationFrame(() => App.processBatch(elements, endIndex))
      } else {
        Core.domManager.optimizeSidebar()
      }
    },

    /**
     * 重试处理内容
     * @param {number} retryCount - 重试次数
     */
    retryProcessContent: (retryCount) => {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      if (articleBodies.length > 0) {
        App.processBatch(articleBodies, 0)
      } else if (retryCount < 3) {
        setTimeout(() => App.retryProcessContent(retryCount + 1), 100)
      }
    },

    /**
     * 应用 HTML 替换规则
     * 第一优先级：HTML级别强行替换 - 最高优先级，直接替换HTML标签内容
     * @param {HTMLElement} element - 需要应用替换的元素
     * @param {Array} rules - 替换规则数组
     */
    applyHtmlReplacements: (element, rules) => {
      if (!element || !rules || rules.length === 0) return
      let currentHTML = element.innerHTML
      const originalHTML = currentHTML
      rules.forEach((rule) => {
        currentHTML = currentHTML.replace(rule.pattern, rule.replacement)
      })
      if (currentHTML !== originalHTML) {
        element.innerHTML = currentHTML
      }
    },

    /**
     * 扫描和学习新词条
     * 动态发现页面中的新复合词，并将其添加到第二优先级处理系统中
     * @param {HTMLElement} element - 需要扫描的元素
     */
    findAndRegisterCompounds: (element) => {
      if (!element) return
      const htmlContent = element.innerHTML
      let match
      while ((match = Config.regex.patterns.bracket.exec(htmlContent)) !== null) {
        const reading = match[2]
        if (reading && !Config.regex.testers.hasNonKana(reading)) {
          const compound = match[1] + '（' + match[2] + '）' + match[3]
          if (!Core.dataProcessor.dynamicWords.has(compound)) {
            Core.dataProcessor.dynamicWords.add(compound)
            App.addDynamicCompoundWord(compound)
          }
        }
      }
    },

    /**
     * 动态添加复合词到处理系统
     * 将新发现的复合词添加到第二优先级处理系统中，并重建全局正则表达式
     * @param {string} compound - 复合词字符串
     */
    addDynamicCompoundWord: (compound) => {
      const parsed = Core.dataProcessor.parseEntry(compound)
      if (!parsed) return
      const firstChar = parsed.pattern[0]
      const escapedPattern = Utils.regex.escape(parsed.pattern)
      if (parsed.kanji && parsed.reading) {
        const processedWords = Core.dataProcessor.processedWords
        if (!processedWords.segmentWords.has(firstChar)) {
          processedWords.segmentWords.set(firstChar, [])
        }
        const wordData = {
          pattern: parsed.pattern,
          kanji: parsed.kanji,
          reading: parsed.reading,
          regex: new RegExp(escapedPattern, 'g'),
        }
        processedWords.segmentWords.get(firstChar).push(wordData)
        // 预计算结果并存储
        const precomputedResult = Core.textProcessor.segmentWord(parsed.kanji, parsed.reading)
        processedWords.patternResults.set(parsed.pattern, precomputedResult)
        // 重建全局正则表达式
        Core.dataProcessor.rebuildGlobalRegex()
      }
    },

    /**
     * Ruby 转换处理
     * 调用核心文本处理器，按优先级顺序执行所有转换规则
     * @param {HTMLElement} root - 根元素
     */
    processRubyConversion: (root) => {
      // 创建 TreeWalker 以遍历文本节点
      const treeWalker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
        acceptNode: (node) => (node.parentNode.nodeName !== 'SCRIPT' && node.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
      })
      // 收集需要处理的节点信息
      const nodesToProcess = []
      let node
      // 遍历所有文本节点并处理内容 - 调用按优先级排序的文本处理逻辑
      while ((node = treeWalker.nextNode())) {
        const newContent = Core.textProcessor.processContent(node.nodeValue)
        if (newContent !== node.nodeValue) {
          nodesToProcess.push({ node, newContent })
        }
      }
      // 批量处理 DOM 操作
      if (nodesToProcess.length > 0) {
        const groupedByParent = new Map()
        nodesToProcess.forEach(({ node, newContent }) => {
          const parent = node.parentNode
          if (!groupedByParent.has(parent)) {
            groupedByParent.set(parent, [])
          }
          groupedByParent.get(parent).push({ node, newContent })
        })
        // 对每个父元素进行批量处理
        groupedByParent.forEach((nodeGroup, parent) => {
          nodeGroup.sort((a, b) => {
            const position = a.node.compareDocumentPosition(b.node)
            return position & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1
          })
          // 从后往前处理，避免位置偏移问题
          for (let i = nodeGroup.length - 1; i >= 0; i--) {
            const { node, newContent } = nodeGroup[i]
            const fragment = document.createDocumentFragment()
            const tempDiv = document.createElement('div')
            tempDiv.innerHTML = newContent
            while (tempDiv.firstChild) {
              fragment.appendChild(tempDiv.firstChild)
            }
            parent.insertBefore(fragment, node)
            parent.removeChild(node)
          }
        })
      }
    },

    // 初始化振假名显示状态
    initFuriganaDisplay: () => {
      const furiganaVisible = Utils.storage.get(Config.settings.keys.FURIGANA_VISIBLE, true)
      App.state.furiganaVisible = furiganaVisible
      if (!furiganaVisible) {
        Styles.manager.toggleFurigana(false)
      }
    },
  }

  // ========================================
  // 应用程序启动，根据 DOM 加载状态选择合适的启动时机
  // ========================================

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', App.init, { once: true })
  } else {
    // DOM 已经加载完成，直接执行
    App.init()
  }
})()
